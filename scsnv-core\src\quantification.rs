use crate::fragment::Fragment;
use crate::snv::{SNVs, SNV};
use crate::genome::{GenomeBaseIndex};
use crate::decode::{SNVProcessor};
use std::path::Path;
use anyhow::Result;
use nalgebra_sparse::CsrMatrix;
use anndata::{
    data::array::utils::{from_csr_data, to_csr_data},
    AnnDataOp, ArrayData, AxisArraysOp, ElemCollectionOp,
};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use itertools::Itertools;
use bed_utils::bed::BEDLike;
use indicatif::{ProgressBar, ProgressStyle, ProgressDrawTarget, ProgressIterator};
use log::warn;

#[derive(Default)]
pub struct SNVQCMetrics {
    pub total_fragments: u64,
    pub total_barcodes: u64,
    pub total_mutations: u64,
    pub unique_mutations: u64,
    pub fragments_with_snvs: u64,
}

/// Import and quantify SNV mutations from fragments
/// This function processes fragments containing SNV information, groups them by barcode,
/// and quantifies mutation counts across all barcodes.
pub fn import_snv_mutations<I, P>(
    anndata: &A,
    fragments: I,
    //white_list: Option<&HashSet<String>>,
    min_num_fragments: u64,
    chunk_size: usize,
    //chrom_sizes: &ChromSizes,
) -> Result<()>
where
    A: AnnDataOp,
    I: Iterator<Item = Fragment>,
{
    let spinner = ProgressBar::with_draw_target(None, ProgressDrawTarget::stderr_with_hz(1))
        .with_style(
            ProgressStyle::with_template(
                "{spinner} Processed {human_pos} barcodes in {elapsed} ({per_sec}) ...",
            )
            .unwrap(),
        );

    //let mut scanned_barcodes = HashSet::new();
    //let mut all_qc_metrics = Vec::new();
    // For now, we use the human genome index as default.
    //let genome_index = GenomeBaseIndex::new(chrom_sizes);
    let genome_index = GenomeBaseIndex::create_human_genome_index();
    let mut saved_barcodes = Vec::new();
    // Group fragments by barcode and filter with whitelist
    let frag_grouped = fragments
        .filter(|x| x.end() > x.start() && !x.is_all_matched()) // Filter valid fragments with positive length
        .chunk_by(|x| x.name().unwrap_or("").to_string());
    
    // Process fragments in chunks
    //let mut all_mutation_counts = Vec::new();
    
    // Collect barcode groups first, then process in chunks
    let barcode_groups: Vec<(String, Vec<Fragment>)> = frag_grouped
        .into_iter()
        .progress_with(spinner)
        //.filter(|(key, _)| white_list.map_or(true, |x| x.contains(key)))
        .map(|(barcode, frags)| (barcode, frags.collect()))
        .collect();
    
    let frag_chunked = barcode_groups
        .into_iter()
        .chunks(chunk_size);
    
    let mut arrays = frag_chunked
        .into_iter()
        .map(|chunk| {
            let chunk_data: Vec<(String, Vec<Fragment>)> = chunk
                .map(|(barcode, frags)| (barcode, frags))
                .collect();
            make_arraydata::<u64>(
                chunk_data,
                &genome_index,
                &mut saved_barcodes,
            )
        })
        .peekable();
    
    if arrays.peek().is_some() {
        anndata.set_obs_names(saved_barcodes.into())?;
    } else {
        warn!("No barcodes passed the QC filter. No data is imported.");
    }
    Ok(())
}


fn make_arraydata<V>(
    data: Vec<(String,Vec<Fragment>)>,
    genome_index: &GenomeBaseIndex,
    saved_barcodes: &mut Vec<String>,
) -> ArrayData
where
    V: TryFrom<u64> + Ord + Send + Sync,
    ArrayData: From<anndata::data::CsrNonCanonical<V>>,
    ArrayData: From<nalgebra_sparse::CsrMatrix<V>>,
    <V as TryFrom<u64>>::Error: std::fmt::Debug,
{
    let num_features: usize = genome_index.len();
    let result: Vec<_> = data
        .into_par_iter()
        .map(|(barcode, x)| {
            (
                barcode,
                count_fragments::<V>(&genome_index, x),
            )
        })
        .collect();
    let counts = result
        .into_iter()
        .filter_map(|(barcode, values)| {
            match values {
                Ok(values) => {
                    saved_barcodes.push(barcode);
                    Some((barcode, values))
                },
                Err(_) => None,
            }
        })
        .collect::<Vec<_>>();
    let (r, c, offset, ind, data) = to_csr_data(counts.into_iter().map(|(_, values)| values), num_features);
    ArrayData::from(from_csr_data(r, c, offset, ind, data).unwrap())
}


fn count_fragments<V>(
    genome_index: &GenomeBaseIndex,
    fragments: Vec<Fragment>,
) -> Result<Vec<(usize, V)>>
where
    V: TryFrom<u64> + Ord + Send,
    <V as TryFrom<u64>>::Error: std::fmt::Debug,
{
    let processor = SNVProcessor::new(50);
    let mut all_snvs = Vec::new();
    fragments.into_iter().for_each(|f| {
        let chrom = &f.chrom;
        if genome_index.contain_chrom(chrom) {
            if let Ok(snvs) = SNVs::try_from(f) {
                all_snvs.extend(snvs.snvs().clone());
            }
        }
    });
    let encoded_snvs = processor.process_snvs(all_snvs)?;
    let arraydata = processor.to_arraydata(&encoded_snvs)?;
    Ok(arraydata)
}



/* 
#[cfg(test)]
mod tests {
    use super::*;
    use crate::snv::{SNV, Mutation};
    use std::collections::HashSet;
    use tempfile::NamedTempFile;

    fn create_test_fragment(
        chrom: &str,
        start: u64,
        end: u64,
        barcode: &str,
    ) -> Fragment {
        let mut fragment = Fragment::new(chrom, start, end);
        fragment.barcode = Some(barcode.to_string());
        fragment
    }

    fn create_test_snv(chrom: &str, position: u64, mutation: Mutation, count: u8) -> SNV {
        SNV::new(chrom.to_string(), position, mutation, count)
    }

    #[test]
    fn test_snv_qc_metrics_default() {
        let metrics = SNVQCMetrics::default();
        assert_eq!(metrics.total_fragments, 0);
        assert_eq!(metrics.total_barcodes, 0);
        assert_eq!(metrics.total_mutations, 0);
        assert_eq!(metrics.unique_mutations, 0);
        assert_eq!(metrics.fragments_with_snvs, 0);
    }

    #[test]
    fn test_import_snv_mutations_empty_iterator() {
        let fragments = std::iter::empty();
        let temp_file = NamedTempFile::new().unwrap();
        let output_path = temp_file.path();
        
        let result = import_snv_mutations(fragments, output_path, None, 0, 100);
        
        assert!(result.is_ok());
        let qc = result.unwrap();
        assert_eq!(qc.total_fragments, 0);
        assert_eq!(qc.total_barcodes, 0);
        assert_eq!(qc.total_mutations, 0);
        assert_eq!(qc.unique_mutations, 0);
        assert_eq!(qc.fragments_with_snvs, 0);
    }

    #[test]
    fn test_import_snv_mutations_invalid_fragments() {
        // Create fragments with invalid length (start >= end)
        let fragments = vec![
            Fragment::new("chr1", 200, 100), // Invalid: start > end
            Fragment::new("chr1", 100, 100), // Invalid: start == end
        ];
        
        let temp_file = NamedTempFile::new().unwrap();
        let output_path = temp_file.path();
        
        let result = import_snv_mutations(fragments.into_iter(), output_path, None, 0, 100);
        
        assert!(result.is_ok());
        let qc = result.unwrap();
        // Should be filtered out
        assert_eq!(qc.total_fragments, 0);
        assert_eq!(qc.total_barcodes, 0);
    }

    #[test]
    fn test_import_snv_mutations_valid_fragments() {
        let barcode1 = "AAAA";
        let barcode2 = "TTTT";
        
        let fragments = vec![
            create_test_fragment("chr1", 100, 200, barcode1),
            create_test_fragment("chr1", 300, 400, barcode1),
            create_test_fragment("chr2", 100, 200, barcode2),
        ];
        
        let temp_file = NamedTempFile::new().unwrap();
        let output_path = temp_file.path();
        
        let result = import_snv_mutations(fragments.into_iter(), output_path, None, 0, 100);
        
        assert!(result.is_ok());
        let qc = result.unwrap();
        assert_eq!(qc.total_fragments, 3);
        assert_eq!(qc.total_barcodes, 2);
        // SNV detection depends on successful parsing, may be 0
        assert!(qc.fragments_with_snvs <= 3);
    }

    #[test]
    fn test_import_snv_mutations_with_whitelist() {
        let barcode1 = "AAAA";
        let barcode2 = "TTTT";
        let barcode3 = "CCCC";
        
        let fragments = vec![
            create_test_fragment("chr1", 100, 200, barcode1),
            create_test_fragment("chr1", 300, 400, barcode2),
            create_test_fragment("chr2", 100, 200, barcode3),
        ];
        
        // Only allow barcode1 and barcode3
        let mut whitelist = HashSet::new();
        whitelist.insert(barcode1.to_string());
        whitelist.insert(barcode3.to_string());
        
        let temp_file = NamedTempFile::new().unwrap();
        let output_path = temp_file.path();
        
        let result = import_snv_mutations(fragments.into_iter(), output_path, Some(&whitelist), 0, 100);
        
        assert!(result.is_ok());
        let qc = result.unwrap();
        assert_eq!(qc.total_fragments, 2); // Only barcode1 and barcode3
        assert_eq!(qc.total_barcodes, 2);
    }

    #[test]
    fn test_import_snv_mutations_min_fragments_filter() {
        let barcode1 = "AAAA";
        let barcode2 = "TTTT";
        
        let fragments = vec![
            create_test_fragment("chr1", 100, 200, barcode1), // 1 fragment
            create_test_fragment("chr1", 300, 400, barcode2), // 1 fragment  
            create_test_fragment("chr1", 500, 600, barcode2), // 2nd fragment for barcode2
            create_test_fragment("chr1", 700, 800, barcode2), // 3rd fragment for barcode2
        ];
        
        let temp_file = NamedTempFile::new().unwrap();
        let output_path = temp_file.path();
        
        let result = import_snv_mutations(fragments.into_iter(), output_path, None, 2, 100);
        
        assert!(result.is_ok());
        let qc = result.unwrap();
        // Only barcode2 should pass the min_fragments filter
        assert_eq!(qc.total_barcodes, 1);
        assert_eq!(qc.total_fragments, 3);
    }

    #[test]
    fn test_chunking_behavior() {
        let barcode1 = "AAAA";
        let barcode2 = "TTTT";
        let barcode3 = "CCCC";
        
        let fragments = vec![
            create_test_fragment("chr1", 100, 200, barcode1),
            create_test_fragment("chr1", 300, 400, barcode2),
            create_test_fragment("chr1", 500, 600, barcode3),
        ];
        
        let temp_file = NamedTempFile::new().unwrap();
        let output_path = temp_file.path();
        
        // Test with small chunk size to ensure chunking works
        let result = import_snv_mutations(fragments.into_iter(), output_path, None, 0, 1);
        
        assert!(result.is_ok());
        let qc = result.unwrap();
        assert_eq!(qc.total_barcodes, 3);
        assert_eq!(qc.total_fragments, 3);
    }
}

*/